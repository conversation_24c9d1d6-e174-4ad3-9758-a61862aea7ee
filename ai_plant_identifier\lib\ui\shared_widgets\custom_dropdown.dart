import 'package:flutter/material.dart';
import '../../utils/constants.dart';

class CustomDropdown extends StatelessWidget {
  final String hint;
  final String? value;
  final List<String> items;
  final Function(String?) onChanged;
  final IconData? prefixIcon;
  final String? label;
  final bool enabled;
  final String? errorText;

  const CustomDropdown({
    super.key,
    required this.hint,
    required this.value,
    required this.items,
    required this.onChanged,
    this.prefixIcon,
    this.label,
    this.enabled = true,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
        DropdownButtonFormField<String>(
          value: value,
          hint: Text(
            hint,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 16,
            ),
          ),
          decoration: InputDecoration(
            prefixIcon: prefixIcon != null 
                ? Icon(
                    prefixIcon,
                    color: AppConstants.primaryGreen,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryGreen, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.errorColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            filled: true,
            fillColor: enabled ? Colors.grey.shade50 : Colors.grey.shade100,
            errorText: errorText,
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(
                item,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppConstants.textPrimary,
                ),
              ),
            );
          }).toList(),
          onChanged: enabled ? onChanged : null,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: enabled ? AppConstants.primaryGreen : Colors.grey.shade400,
          ),
          dropdownColor: Colors.white,
          style: const TextStyle(
            fontSize: 16,
            color: AppConstants.textPrimary,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select an option';
            }
            return null;
          },
        ),
      ],
    );
  }
}

class CustomMultiSelectDropdown extends StatefulWidget {
  final String hint;
  final List<String> selectedValues;
  final List<String> items;
  final Function(List<String>) onChanged;
  final IconData? prefixIcon;
  final String? label;
  final bool enabled;

  const CustomMultiSelectDropdown({
    super.key,
    required this.hint,
    required this.selectedValues,
    required this.items,
    required this.onChanged,
    this.prefixIcon,
    this.label,
    this.enabled = true,
  });

  @override
  State<CustomMultiSelectDropdown> createState() => _CustomMultiSelectDropdownState();
}

class _CustomMultiSelectDropdownState extends State<CustomMultiSelectDropdown> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
        InkWell(
          onTap: widget.enabled ? _showMultiSelectDialog : null,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              color: widget.enabled ? Colors.grey.shade50 : Colors.grey.shade100,
            ),
            child: Row(
              children: [
                if (widget.prefixIcon != null) ...[
                  Icon(
                    widget.prefixIcon,
                    color: AppConstants.primaryGreen,
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                ],
                Expanded(
                  child: Text(
                    widget.selectedValues.isEmpty
                        ? widget.hint
                        : widget.selectedValues.join(', '),
                    style: TextStyle(
                      fontSize: 16,
                      color: widget.selectedValues.isEmpty
                          ? Colors.grey.shade600
                          : AppConstants.textPrimary,
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: widget.enabled ? AppConstants.primaryGreen : Colors.grey.shade400,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showMultiSelectDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        List<String> tempSelected = List.from(widget.selectedValues);
        
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(widget.label ?? 'Select Items'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.items.map((item) {
                    return CheckboxListTile(
                      title: Text(item),
                      value: tempSelected.contains(item),
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            tempSelected.add(item);
                          } else {
                            tempSelected.remove(item);
                          }
                        });
                      },
                      activeColor: AppConstants.primaryGreen,
                    );
                  }).toList(),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    widget.onChanged(tempSelected);
                    Navigator.of(context).pop();
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
