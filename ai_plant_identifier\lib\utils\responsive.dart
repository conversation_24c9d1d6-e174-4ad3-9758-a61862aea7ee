import 'package:flutter/material.dart';

class ResponsiveHelper {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static double getProportionateScreenWidth(BuildContext context, double inputWidth) {
    double screenWidth = MediaQuery.of(context).size.width;
    // 375 is the layout width that the designer used (iPhone X)
    return (inputWidth / 375.0) * screenWidth;
  }

  static double getProportionateScreenHeight(BuildContext context, double inputHeight) {
    double screenHeight = MediaQuery.of(context).size.height;
    // 812 is the layout height that the designer used (iPhone X)
    return (inputHeight / 812.0) * screenHeight;
  }

  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24.0);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    if (isMobile(context)) {
      return baseFontSize;
    } else if (isTablet(context)) {
      return baseFontSize * 1.1;
    } else {
      return baseFontSize * 1.2;
    }
  }

  static int getGridCrossAxisCount(BuildContext context) {
    if (isMobile(context)) {
      return 2;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 4;
    }
  }

  static double getCardWidth(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    if (isMobile(context)) {
      return screenWidth - 32; // Full width minus padding
    } else if (isTablet(context)) {
      return (screenWidth - 64) / 2; // Two cards per row
    } else {
      return (screenWidth - 96) / 3; // Three cards per row
    }
  }

  static double getImageSize(BuildContext context) {
    if (isMobile(context)) {
      return getScreenWidth(context) * 0.7;
    } else if (isTablet(context)) {
      return 300;
    } else {
      return 400;
    }
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static double getBottomNavigationHeight(BuildContext context) {
    return kBottomNavigationBarHeight + MediaQuery.of(context).padding.bottom;
  }

  static double getAppBarHeight(BuildContext context) {
    return kToolbarHeight + MediaQuery.of(context).padding.top;
  }

  static double getAvailableHeight(BuildContext context) {
    return getScreenHeight(context) - 
           getAppBarHeight(context) - 
           getBottomNavigationHeight(context);
  }
}
