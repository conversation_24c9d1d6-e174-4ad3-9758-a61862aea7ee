import 'package:hive/hive.dart';
import '../models/plant_model.dart';
import '../models/history_model.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();

  factory LocalStorageService() => _instance;

  LocalStorageService._internal();

  // Plant operations
  Future<void> addPlantToHistory(PlantModel plant) async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.add(plant);
  }

  List<PlantModel> getAllPlants() {
    final box = Hive.box<PlantModel>('plantsBox');
    return box.values.toList().reversed.toList(); // Most recent first
  }

  Future<void> updatePlant(int index, PlantModel plant) async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.putAt(index, plant);
  }

  Future<void> deletePlant(int index) async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.deleteAt(index);
  }

  Future<void> clearPlantHistory() async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.clear();
  }

  // History operations
  Future<void> addHistoryEntry(HistoryModel history) async {
    final box = Hive.box<HistoryModel>('historyBox');
    await box.add(history);
  }

  List<HistoryModel> getAllHistory() {
    final box = Hive.box<HistoryModel>('historyBox');
    return box.values.toList().reversed.toList(); // Most recent first
  }

  Future<void> clearHistory() async {
    final box = Hive.box<HistoryModel>('historyBox');
    await box.clear();
  }

  // Statistics
  int getTotalPlantsIdentified() {
    return getAllPlants().length;
  }

  Map<String, int> getPlantTypeStats() {
    final plants = getAllPlants();
    final stats = <String, int>{};
    
    for (final plant in plants) {
      stats[plant.type] = (stats[plant.type] ?? 0) + 1;
    }
    
    return stats;
  }

  List<PlantModel> getRecentPlants({int limit = 5}) {
    final plants = getAllPlants();
    return plants.take(limit).toList();
  }
}
