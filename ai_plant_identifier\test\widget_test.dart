// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:ai_plant_identifier/main.dart';

void main() {
  testWidgets('App loads without crashing', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp(showOnboarding: true));

    // Verify that the onboarding screen loads
    expect(find.text('Skip'), findsOneWidget);
  });

  testWidgets('App loads directly to home when onboarding is skipped', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp(showOnboarding: false));

    // Verify that the home screen loads
    expect(find.text('Home'), findsOneWidget);
  });
}
