import 'package:hive/hive.dart';

part 'history_model.g.dart';

@HiveType(typeId: 1)
class HistoryModel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String plantId;

  @HiveField(2)
  final DateTime timestamp;

  @HiveField(3)
  final String action; // 'identified', 'saved', 'updated'

  @HiveField(4)
  final Map<String, dynamic>? metadata;

  HistoryModel({
    required this.id,
    required this.plantId,
    required this.timestamp,
    required this.action,
    this.metadata,
  });
}
