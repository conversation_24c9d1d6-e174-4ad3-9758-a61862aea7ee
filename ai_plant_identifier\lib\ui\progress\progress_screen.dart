import 'package:flutter/material.dart';
import '../../services/local_storage_service.dart';
import '../../utils/constants.dart';
import '../../utils/responsive.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final LocalStorageService _storageService = LocalStorageService();

  @override
  Widget build(BuildContext context) {
    final totalPlants = _storageService.getTotalPlantsIdentified();
    final plantTypeStats = _storageService.getPlantTypeStats();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Progress'),
        backgroundColor: AppConstants.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: totalPlants == 0 ? _buildEmptyState() : _buildProgressContent(totalPlants, plantTypeStats),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              'No Progress Data Yet',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppConstants.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Start identifying plants to track your progress.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressContent(int totalPlants, Map<String, int> plantTypeStats) {
    return SingleChildScrollView(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(totalPlants),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildPlantTypesSection(plantTypeStats),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildAchievementsSection(totalPlants),
        ],
      ),
    );
  }

  Widget _buildStatsCard(int totalPlants) {
    return Card(
      elevation: AppConstants.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppConstants.primaryGreen.withOpacity(0.1),
              AppConstants.lightGreen.withOpacity(0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.eco,
              size: 60,
              color: AppConstants.primaryGreen,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              totalPlants.toString(),
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                color: AppConstants.primaryGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Plants Identified',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlantTypesSection(Map<String, int> plantTypeStats) {
    if (plantTypeStats.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Plant Types Discovered',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ...plantTypeStats.entries.map((entry) => _buildPlantTypeItem(entry.key, entry.value)),
      ],
    );
  }

  Widget _buildPlantTypeItem(String type, int count) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryGreen.withOpacity(0.1),
          child: Icon(
            _getPlantTypeIcon(type),
            color: AppConstants.primaryGreen,
          ),
        ),
        title: Text(type),
        trailing: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
          decoration: BoxDecoration(
            color: AppConstants.primaryGreen,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Text(
            count.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAchievementsSection(int totalPlants) {
    final achievements = _getAchievements(totalPlants);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Achievements',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ...achievements.map((achievement) => _buildAchievementItem(achievement)),
      ],
    );
  }

  Widget _buildAchievementItem(Map<String, dynamic> achievement) {
    final bool isUnlocked = achievement['unlocked'] as bool;
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isUnlocked 
              ? AppConstants.primaryGreen.withOpacity(0.1)
              : Colors.grey.shade200,
          child: Icon(
            achievement['icon'] as IconData,
            color: isUnlocked ? AppConstants.primaryGreen : Colors.grey,
          ),
        ),
        title: Text(
          achievement['title'] as String,
          style: TextStyle(
            color: isUnlocked ? AppConstants.textPrimary : Colors.grey,
            fontWeight: isUnlocked ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        subtitle: Text(
          achievement['description'] as String,
          style: TextStyle(
            color: isUnlocked ? AppConstants.textSecondary : Colors.grey,
          ),
        ),
        trailing: isUnlocked 
            ? const Icon(Icons.check_circle, color: AppConstants.successColor)
            : const Icon(Icons.lock_outline, color: Colors.grey),
      ),
    );
  }

  IconData _getPlantTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'flower':
        return Icons.local_florist;
      case 'tree':
        return Icons.park;
      case 'shrub':
        return Icons.grass;
      case 'succulent':
        return Icons.eco;
      case 'herb':
        return Icons.spa;
      case 'vine':
        return Icons.nature;
      case 'fern':
        return Icons.forest;
      case 'grass':
        return Icons.grass;
      case 'moss':
        return Icons.nature_people;
      default:
        return Icons.local_florist;
    }
  }

  List<Map<String, dynamic>> _getAchievements(int totalPlants) {
    return [
      {
        'title': 'First Discovery',
        'description': 'Identify your first plant',
        'icon': Icons.star,
        'unlocked': totalPlants >= 1,
      },
      {
        'title': 'Plant Explorer',
        'description': 'Identify 5 different plants',
        'icon': Icons.explore,
        'unlocked': totalPlants >= 5,
      },
      {
        'title': 'Botanist',
        'description': 'Identify 10 different plants',
        'icon': Icons.science,
        'unlocked': totalPlants >= 10,
      },
      {
        'title': 'Plant Expert',
        'description': 'Identify 25 different plants',
        'icon': Icons.school,
        'unlocked': totalPlants >= 25,
      },
      {
        'title': 'Master Botanist',
        'description': 'Identify 50 different plants',
        'icon': Icons.emoji_events,
        'unlocked': totalPlants >= 50,
      },
    ];
  }
}
