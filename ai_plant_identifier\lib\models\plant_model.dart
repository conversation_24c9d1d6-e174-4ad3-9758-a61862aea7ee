import 'package:hive/hive.dart';

part 'plant_model.g.dart';

@HiveType(typeId: 0)
class PlantModel {
  @HiveField(0)
  final String imagePath;

  @HiveField(1)
  final String type;

  @HiveField(2)
  final DateTime timestamp;

  @HiveField(3)
  final String? identifiedName;

  @HiveField(4)
  final String? scientificName;

  @HiveField(5)
  final String? description;

  @HiveField(6)
  final double? confidence;

  PlantModel({
    required this.imagePath,
    required this.type,
    required this.timestamp,
    this.identifiedName,
    this.scientificName,
    this.description,
    this.confidence,
  });

  PlantModel copyWith({
    String? imagePath,
    String? type,
    DateTime? timestamp,
    String? identifiedName,
    String? scientificName,
    String? description,
    double? confidence,
  }) {
    return PlantModel(
      imagePath: imagePath ?? this.imagePath,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      identifiedName: identifiedName ?? this.identifiedName,
      scientificName: scientificName ?? this.scientificName,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
    );
  }
}
